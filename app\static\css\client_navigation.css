/* Enhanced Navigation Styles for Client Interface - Minimalist Theme */

/* Main Navigation Styling with Clean Black */
.navbar-dark.bg-primary {
    background: #000000 !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #dee2e6;
}

/* Add spacing between nav items */
.navbar-nav .nav-item {
    margin-right: 0.25rem;
}

/* Enhance dropdown menus with minimalist styling */
.dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    padding: 0.75rem 0;
    border: 1px solid #dee2e6;
    min-width: 260px;
    background: #ffffff;
}

/* Style dropdown headers with clean typography */
.dropdown-header {
    font-weight: 600;
    color: #212529;
    padding: 0.75rem 1.25rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Inter', sans-serif;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 0.5rem;
}

/* Style dropdown items with hover effects */
.dropdown-item {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
    border-radius: 6px;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.dropdown-item:hover {
    background: #f8f9fa;
    color: #212529;
    transform: translateX(2px);
}

.dropdown-item i {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
    color: #6c757d;
    transition: color 0.3s ease;
}

.dropdown-item:hover i {
    color: #dc3545;
}

/* Active dropdown item */
.dropdown-item.active,
.dropdown-item:active {
    background-color: #007bff;
    color: white;
}

.dropdown-item.active i,
.dropdown-item:active i {
    color: white;
}

/* Breadcrumb styling */
.breadcrumb-wrapper {
    background-color: #f8f9fa;
    padding: 0.75rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 1rem;
}

.breadcrumb {
    margin-bottom: 0;
    background-color: transparent;
    padding: 0;
}

.breadcrumb-item {
    font-size: 0.875rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    font-weight: bold;
}

.breadcrumb-item.active {
    color: #007bff;
    font-weight: 500;
}

/* Quick actions styling */
.quick-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.quick-actions .btn {
    border-radius: 0.375rem;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.quick-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Language switcher styling */
.language-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.language-btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .navbar-nav .nav-item {
        margin-right: 0;
    }

    .dropdown-menu {
        border: none;
        box-shadow: none;
        padding-left: 1rem;
        min-width: auto;
    }

    .quick-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .breadcrumb-wrapper {
        display: none;
    }
}

/* Highlight active navigation item */
.nav-item.active .nav-link,
.nav-item .nav-link.active {
    font-weight: 600;
    position: relative;
}

.nav-item.active .nav-link:after,
.nav-item .nav-link.active:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 1px;
}

/* Color coding for different sections - Minimalist Theme */
/* Appointments - Red Highlight */
.nav-link[href*="appointments"] i {
    color: #dc3545;
}

/* Finances - Red Highlight */
.nav-link[href*="invoices"] i,
.nav-link[href*="subscriptions"] i {
    color: #dc3545;
}

/* Profile - Red Highlight */
.nav-link[href*="profile"] i {
    color: #dc3545;
}

/* Dashboard - Red Highlight */
.nav-link[href*="dashboard"] i {
    color: #dc3545;
}

/* TECFÉE Sessions - Red Highlight */
.nav-link[href*="tecfee"] i {
    color: #dc3545;
}

/* Notification styles */
.nav-link .badge {
    font-size: 0.65rem;
    position: relative;
    top: -2px;
}

/* Smooth transitions */
.nav-link,
.dropdown-item,
.btn {
    transition: all 0.2s ease;
}

/* Focus states for accessibility */
.nav-link:focus,
.dropdown-item:focus,
.btn:focus {
    outline: 2px solid rgba(0, 123, 255, 0.5);
    outline-offset: 2px;
}

/* Loading states */
.nav-link.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Enhanced hover effects */
.nav-link:hover {
    transform: translateY(-1px);
}

.dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transform: translateX(2px);
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background-color: #28a745;
}

.status-indicator.offline {
    background-color: #6c757d;
}

.status-indicator.busy {
    background-color: #ffc107;
}

/* Card enhancements for dashboard */
.dashboard-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: none;
    border-radius: 0.5rem;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Icon animations */
.nav-link i,
.dropdown-item i {
    transition: transform 0.2s ease;
}

.nav-link:hover i,
.dropdown-item:hover i {
    transform: scale(1.1);
}
