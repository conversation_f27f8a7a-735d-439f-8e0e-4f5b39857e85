/* Professional Earth-Tone Client Interface Theme */
/* Neutral Color Palette with Sophisticated Design */

/* ===== COLOR VARIABLES ===== */
:root {
    /* Primary Earth Tones */
    --primary-earth: #8b7355;
    --primary-earth-dark: #6b5b47;
    --primary-earth-light: #f5f3f0;
    
    /* Secondary Earth Tones */
    --warm-beige: #e8e2d5;
    --warm-beige-dark: #d4c4a8;
    --warm-beige-light: #f7f5f1;
    
    --sage-green: #9aaa97;
    --sage-green-dark: #7a8a77;
    --sage-green-light: #f2f4f2;
    
    --warm-gray: #a69b8e;
    --warm-gray-dark: #8a7f72;
    --warm-gray-light: #f4f2ef;
    
    --cream: #faf8f5;
    --cream-dark: #f0ede8;
    
    /* Professional Neutrals */
    --soft-white: #fefefe;
    --charcoal: #3c3c3c;
    --medium-gray: #6c6c6c;
    --light-gray: #e5e5e5;
    
    /* Accent Colors */
    --terracotta: #c4a484;
    --olive: #8b956d;
    --stone: #b8aca0;
    --taupe: #9c8f83;
    
    /* Shadows & Effects */
    --soft-shadow: 0 4px 20px rgba(139, 115, 85, 0.12);
    --elegant-shadow: 0 8px 32px rgba(139, 115, 85, 0.15);
    --inner-glow: inset 0 1px 3px rgba(255, 255, 255, 0.8);
    
    /* Typography */
    --font-elegant: 'Playfair Display', serif;
    --font-modern: 'Inter', sans-serif;
    --font-accent: 'Crimson Text', serif;
}

/* ===== GOOGLE FONTS IMPORT ===== */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&family=Crimson+Text:wght@400;600&display=swap');

/* ===== GLOBAL CLIENT INTERFACE STYLING ===== */
.client-interface {
    font-family: var(--font-modern);
    background: linear-gradient(135deg, var(--cream) 0%, var(--warm-beige-light) 100%);
    min-height: 100vh;
}

/* ===== NAVIGATION ENHANCEMENTS ===== */
.navbar-dark.bg-primary {
    background: linear-gradient(135deg, var(--primary-earth-dark) 0%, var(--warm-gray-dark) 100%) !important;
    box-shadow: var(--soft-shadow);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
    font-family: var(--font-elegant);
    font-weight: 600;
    color: var(--soft-white) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== SIDEBAR STYLING ===== */
.sidebar {
    background: linear-gradient(180deg, var(--soft-white) 0%, var(--cream) 100%);
    border-right: 1px solid var(--warm-beige);
    box-shadow: var(--soft-shadow);
}

.sidebar .nav-link {
    color: var(--charcoal);
    border-radius: 12px;
    margin: 4px 8px;
    padding: 12px 16px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sidebar .nav-link:hover {
    background: linear-gradient(135deg, var(--warm-beige-light) 0%, var(--sage-green-light) 100%);
    color: var(--charcoal);
    transform: translateX(4px);
    box-shadow: var(--soft-shadow);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, var(--warm-beige) 0%, var(--sage-green) 100%);
    color: var(--charcoal);
    box-shadow: var(--elegant-shadow);
}

/* ===== CARD COMPONENTS ===== */
.card {
    border: none;
    border-radius: 16px;
    box-shadow: var(--soft-shadow);
    background: var(--soft-white);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--elegant-shadow);
}

.card-header {
    background: linear-gradient(135deg, var(--warm-beige) 0%, var(--sage-green) 100%) !important;
    border-bottom: none;
    padding: 20px 24px;
    color: var(--charcoal) !important;
    font-family: var(--font-elegant);
    font-weight: 600;
    font-size: 1.1rem;
}

.card-body {
    padding: 24px;
    background: var(--soft-white);
}

/* ===== BUTTONS ===== */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-earth) 0%, var(--warm-gray-dark) 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 500;
    color: var(--soft-white);
    box-shadow: var(--soft-shadow);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-earth-dark) 0%, var(--taupe) 100%);
    transform: translateY(-1px);
    box-shadow: var(--elegant-shadow);
}

.btn-success {
    background: linear-gradient(135deg, var(--sage-green) 0%, var(--olive) 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 500;
    color: var(--soft-white);
    box-shadow: var(--soft-shadow);
}

.btn-outline-primary {
    border: 2px solid var(--primary-earth);
    color: var(--primary-earth);
    border-radius: 8px;
    padding: 10px 22px;
    font-weight: 500;
    background: transparent;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, var(--warm-beige) 0%, var(--sage-green-light) 100%);
    border-color: var(--primary-earth-dark);
    color: var(--charcoal);
    transform: translateY(-1px);
}

/* ===== FORM ELEMENTS ===== */
.form-control {
    border: 2px solid var(--warm-beige);
    border-radius: 8px;
    padding: 12px 16px;
    background: var(--soft-white);
    transition: all 0.3s ease;
    font-family: var(--font-modern);
}

.form-control:focus {
    border-color: var(--primary-earth);
    box-shadow: 0 0 0 0.2rem rgba(139, 115, 85, 0.15);
    background: var(--warm-beige-light);
}

.form-label {
    font-weight: 500;
    color: var(--charcoal);
    margin-bottom: 8px;
    font-family: var(--font-modern);
}

/* ===== ALERTS ===== */
.alert {
    border: none;
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(135deg, var(--sage-green-light) 0%, var(--sage-green) 100%);
    color: var(--charcoal);
    border-left: 4px solid var(--olive);
}

.alert-info {
    background: linear-gradient(135deg, var(--warm-beige-light) 0%, var(--warm-beige) 100%);
    color: var(--charcoal);
    border-left: 4px solid var(--primary-earth);
}

.alert-warning {
    background: linear-gradient(135deg, var(--warm-gray-light) 0%, var(--warm-gray) 100%);
    color: var(--charcoal);
    border-left: 4px solid var(--terracotta);
}

.alert-danger {
    background: linear-gradient(135deg, var(--cream-dark) 0%, var(--warm-beige) 100%);
    color: var(--charcoal);
    border-left: 4px solid var(--taupe);
}

/* ===== TABLES ===== */
.table {
    background: var(--soft-white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--soft-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--warm-beige) 0%, var(--sage-green) 100%);
    color: var(--charcoal);
    border: none;
    padding: 16px;
    font-weight: 600;
    font-family: var(--font-elegant);
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: var(--cream);
    transform: scale(1.005);
}

.table tbody td {
    padding: 16px;
    border-color: var(--warm-beige);
    vertical-align: middle;
}

/* ===== BADGES ===== */
.badge {
    border-radius: 6px;
    padding: 6px 12px;
    font-weight: 500;
    font-size: 0.75rem;
}

.badge.bg-success {
    background: linear-gradient(135deg, var(--sage-green) 0%, var(--olive) 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, var(--terracotta) 0%, var(--warm-gray) 100%) !important;
    color: var(--soft-white) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, var(--taupe) 0%, var(--stone) 100%) !important;
}

/* ===== DASHBOARD SPECIFIC ===== */
.dashboard-welcome {
    background: linear-gradient(135deg, var(--cream) 0%, var(--warm-beige-light) 100%);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: var(--soft-shadow);
}

.dashboard-welcome h2 {
    font-family: var(--font-elegant);
    color: var(--charcoal);
    margin-bottom: 8px;
}

.dashboard-welcome p {
    color: var(--medium-gray);
    font-size: 1.1rem;
}

/* ===== STATISTICS CARDS ===== */
.stat-card {
    background: linear-gradient(135deg, var(--soft-white) 0%, var(--cream) 100%);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    box-shadow: var(--soft-shadow);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--elegant-shadow);
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    font-family: var(--font-elegant);
    background: linear-gradient(135deg, var(--primary-earth) 0%, var(--warm-gray-dark) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-card .stat-label {
    color: var(--medium-gray);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .card {
        border-radius: 12px;
        margin-bottom: 16px;
    }

    .card-header {
        padding: 16px 20px;
        font-size: 1rem;
    }

    .card-body {
        padding: 20px;
    }

    .btn {
        border-radius: 6px;
        padding: 10px 20px;
    }

    .dashboard-welcome {
        padding: 24px;
        margin-bottom: 24px;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--cream);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--warm-beige) 0%, var(--sage-green) 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-earth) 0%, var(--warm-gray-dark) 100%);
}
