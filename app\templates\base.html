<!-- app/templates/base.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}TutorAide Inc.{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.3.0/css/all.min.css">

    <!-- Google Fonts for proper Google Sign-In button -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <!-- Google Sign-In Button Styles -->
    <style>
        /* Official Google Sign-In Button Styles */
        .google-signin-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-color: #ffffff;
            border: 1px solid #dadce0;
            border-radius: 4px;
            color: #3c4043;
            font-family: 'Roboto', sans-serif;
            font-size: 14px;
            font-weight: 500;
            letter-spacing: 0.25px;
            line-height: 16px;
            padding: 12px 16px;
            text-decoration: none;
            transition: background-color 0.218s, border-color 0.218s, box-shadow 0.218s;
            min-height: 40px;
            width: 100%;
        }

        .google-signin-btn:hover {
            background-color: #f8f9fa;
            border-color: #dadce0;
            box-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.302), 0 4px 8px 3px rgba(60, 64, 67, 0.149);
            color: #3c4043;
            text-decoration: none;
        }

        .google-signin-btn:focus {
            background-color: #f8f9fa;
            border-color: #4285f4;
            box-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.302), 0 4px 8px 3px rgba(60, 64, 67, 0.149);
            color: #3c4043;
            outline: none;
            text-decoration: none;
        }

        .google-signin-btn:active {
            background-color: #f1f3f4;
            border-color: #dadce0;
            box-shadow: 0 2px 6px 2px rgba(60, 64, 67, 0.149);
            color: #3c4043;
        }

        .google-logo {
            margin-right: 12px;
            width: 18px;
            height: 18px;
        }

        /* Large Google Sign-In Button */
        .google-signin-btn-lg {
            font-size: 16px;
            font-weight: 500;
            padding: 16px 24px;
            min-height: 48px;
        }

        .google-signin-btn-lg .google-logo {
            width: 20px;
            height: 20px;
            margin-right: 16px;
        }

        /* Small Google Sign-In Button */
        .google-signin-btn-sm {
            font-size: 13px;
            padding: 8px 12px;
            min-height: 32px;
        }

        .google-signin-btn-sm .google-logo {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }

        /* Dark theme variant */
        .google-signin-btn-dark {
            background-color: #4285f4;
            border-color: #4285f4;
            color: #ffffff;
        }

        .google-signin-btn-dark:hover {
            background-color: #3367d6;
            border-color: #3367d6;
            color: #ffffff;
        }

        .google-signin-btn-dark:focus {
            background-color: #3367d6;
            border-color: #3367d6;
            color: #ffffff;
        }

        .google-signin-btn-dark:active {
            background-color: #2d5aa0;
            border-color: #2d5aa0;
            color: #ffffff;
        }
    </style>

    <!-- Role-specific Navigation CSS -->
    {% if current_user.is_authenticated %}
        {% if current_user.role == 'manager' %}
            <link rel="stylesheet" href="{{ url_for('static', filename='css/manager_navigation.css') }}">
            <link rel="stylesheet" href="{{ url_for('static', filename='css/sidebar.css') }}">
        {% elif current_user.role == 'tutor' %}
            <link rel="stylesheet" href="{{ url_for('static', filename='css/tutor_navigation.css') }}">
            <link rel="stylesheet" href="{{ url_for('static', filename='css/sidebar.css') }}">
        {% elif current_user.role in ['parent', 'client'] %}
            <link rel="stylesheet" href="{{ url_for('static', filename='css/client_navigation.css') }}">
            <link rel="stylesheet" href="{{ url_for('static', filename='css/sidebar.css') }}">
        {% endif %}
    {% endif %}

    <!-- Calendar CSS (FullCalendar) -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.0/main.min.css" rel="stylesheet">

    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Top Navigation Bar (Minimal) -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <!-- Logo and Sidebar Toggle -->
            <div class="d-flex align-items-center">
                {% if current_user.is_authenticated and current_user.role in ['manager', 'tutor', 'client', 'parent'] %}
                <button class="btn btn-link text-white me-3 sidebar-toggle" type="button" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                {% endif %}
                <a class="navbar-brand" href="{{ url_for('main.index') }}">
                    <strong>TutorAide</strong>
                </a>
            </div>

            <!-- Center: Search Bar (Manager only) -->
            {% if current_user.is_authenticated and current_user.role == 'manager' %}
            <div class="flex-grow-1 mx-4 d-none d-md-block">
                <form class="navbar-search-form" action="{{ url_for('manager.global_search') }}" method="GET">
                    <div class="input-group" style="max-width: 400px; margin: 0 auto;">
                        <input class="form-control search-input" type="search" name="q" placeholder="Search clients, tutors, appointments... (Ctrl+K)" aria-label="Search">
                        <button class="btn btn-outline-light" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
            {% endif %}

            <!-- Right: Quick Actions + User Menu -->
            <div class="d-flex align-items-center">
                {% if current_user.is_authenticated and current_user.role == 'manager' %}
                <!-- Quick Actions -->
                <div class="quick-actions-top d-none d-lg-flex me-3">
                    <a href="{{ url_for('manager.new_appointment') }}" class="btn btn-sm btn-success me-2" title="New Appointment">
                        <i class="fas fa-calendar-plus"></i>
                    </a>
                    <a href="{{ url_for('manager.new_client') }}" class="btn btn-sm btn-info me-2" title="New Client">
                        <i class="fas fa-user-plus"></i>
                    </a>
                    <a href="{{ url_for('manager.tutor_payments_list') }}?status=pending" class="btn btn-sm btn-warning me-2" title="Process Payments">
                        <i class="fas fa-money-bill-wave"></i>
                    </a>
                    <a href="{{ url_for('manager.tecfee_dashboard') }}" class="btn btn-sm btn-outline-light" title="TECFÉE">
                        <i class="fas fa-graduation-cap"></i>
                    </a>
                </div>
                {% endif %}

                <!-- User Dropdown -->
                {% if current_user.is_authenticated %}
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <span class="d-none d-md-inline">{{ current_user.email }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        {% if current_user.role == 'manager' %}
                            <li><a class="dropdown-item" href="#">{{ t('navigation.settings') }}</a></li>
                        {% elif current_user.role == 'tutor' %}
                            <li><a class="dropdown-item" href="{{ url_for('tutor.profile') }}">{{ t('navigation.profile') }}</a></li>
                        {% elif current_user.role in ['parent', 'client'] %}
                            <li><a class="dropdown-item" href="{{ url_for('client.profile') }}">{{ t('navigation.profile') }}</a></li>
                        {% endif %}
                        <li><hr class="dropdown-divider"></li>
                        <li class="dropdown-item">
                            <div class="btn-group btn-group-sm w-100" role="group" aria-label="{{ t('language.select') }}">
                                <button type="button" class="btn btn-outline-primary language-btn {% if session.get('language', 'en') == 'en' %}active{% endif %}" data-lang="en">{{ t('language.english') }}</button>
                                <button type="button" class="btn btn-outline-primary language-btn {% if session.get('language', 'en') == 'fr' %}active{% endif %}" data-lang="fr">{{ t('language.french') }}</button>
                            </div>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">{{ t('navigation.logout') }}</a></li>
                    </ul>
                </div>
                {% else %}
                <a class="btn btn-outline-light" href="{{ url_for('auth.login') }}">{{ t('navigation.login') }}</a>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Left Sidebar (Manager only) -->
    {% if current_user.is_authenticated and current_user.role == 'manager' %}
    <div class="sidebar" id="sidebar">
        <div class="sidebar-content">
            <!-- Overview -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="overview">
                    <div class="header-content">
                        <i class="fas fa-chart-pie"></i>
                        <span>Overview</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('manager.dashboard') }}" class="sidebar-link" data-tooltip="Dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Scheduling -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="scheduling">
                    <div class="header-content">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Scheduling</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('manager.schedule') }}" class="sidebar-link" data-tooltip="Calendar">
                            <i class="fas fa-calendar-week"></i>
                            <span>Calendar</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manager.new_appointment') }}" class="sidebar-link" data-tooltip="New Appointment">
                            <i class="fas fa-plus-circle"></i>
                            <span>New Appointment</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manager.time_off_requests') }}" class="sidebar-link" data-tooltip="Time-Off Requests">
                            <i class="fas fa-user-clock"></i>
                            <span>Time-Off Requests</span>
                            {% if pending_time_off_requests and pending_time_off_requests|length > 0 %}
                                <span class="badge bg-danger ms-auto">{{ pending_time_off_requests|length }}</span>
                            {% endif %}
                        </a>
                    </li>
                </ul>
            </div>

            <!-- People -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="people">
                    <div class="header-content">
                        <i class="fas fa-users"></i>
                        <span>People</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('manager.clients_list') }}" class="sidebar-link" data-tooltip="Clients">
                            <i class="fas fa-user-friends"></i>
                            <span>Clients</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manager.dependants_list') }}" class="sidebar-link" data-tooltip="Dependants">
                            <i class="fas fa-child"></i>
                            <span>Dependants</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manager.tutors_list') }}" class="sidebar-link" data-tooltip="Tutors">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <span>Tutors</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Services -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="services">
                    <div class="header-content">
                        <i class="fas fa-book-open"></i>
                        <span>Services</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('manager.services_list') }}" class="sidebar-link" data-tooltip="All Services">
                            <i class="fas fa-list"></i>
                            <span>All Services</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manager.new_tutor_service') }}" class="sidebar-link" data-tooltip="Tutor Assignments">
                            <i class="fas fa-link"></i>
                            <span>Tutor Assignments</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manager.subscription_plans_list') }}" class="sidebar-link" data-tooltip="Subscription Plans">
                            <i class="fas fa-list-alt"></i>
                            <span>Subscription Plans</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Programs -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="programs">
                    <div class="header-content">
                        <i class="fas fa-graduation-cap"></i>
                        <span>Programs</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('manager.tecfee_dashboard') }}" class="sidebar-link" data-tooltip="TECFÉE Dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>TECFÉE Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manager.tecfee_enrollments') }}" class="sidebar-link" data-tooltip="Enrollments">
                            <i class="fas fa-users"></i>
                            <span>Enrollments</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manager.tecfee_group_sessions') }}" class="sidebar-link" data-tooltip="Group Sessions">
                            <i class="fas fa-users-cog"></i>
                            <span>Group Sessions</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Finances -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="finances">
                    <div class="header-content">
                        <i class="fas fa-dollar-sign"></i>
                        <span>Finances</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('manager.invoices_list') }}" class="sidebar-link" data-tooltip="Invoices">
                            <i class="fas fa-file-invoice-dollar"></i>
                            <span>Invoices</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manager.invoices_list') }}?status=unpaid" class="sidebar-link" data-tooltip="Unpaid Invoices">
                            <i class="fas fa-exclamation-circle"></i>
                            <span>Unpaid Invoices</span>
                            {% if unpaid_invoices and unpaid_invoices|length > 0 %}
                                <span class="badge bg-danger ms-auto">{{ unpaid_invoices|length }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manager.subscriptions_list') }}" class="sidebar-link" data-tooltip="Subscriptions">
                            <i class="fas fa-sync"></i>
                            <span>Subscriptions</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manager.tutor_payments_list') }}" class="sidebar-link" data-tooltip="Tutor Payments">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>Tutor Payments</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Reports -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="reports">
                    <div class="header-content">
                        <i class="fas fa-chart-bar"></i>
                        <span>Reports</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('manager.tutor_earnings_report') }}" class="sidebar-link" data-tooltip="Earnings Reports">
                            <i class="fas fa-chart-line"></i>
                            <span>Earnings Reports</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- System Administration -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="admin">
                    <div class="header-content">
                        <i class="fas fa-cogs"></i>
                        <span>Administration</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('main.create_manager') }}" class="sidebar-link" data-tooltip="Create Manager">
                            <i class="fas fa-user-plus"></i>
                            <span>Create Manager</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>
    {% endif %}

    <!-- Left Sidebar (Tutor only) -->
    {% if current_user.is_authenticated and current_user.role == 'tutor' %}
    <div class="sidebar" id="sidebar">
        <div class="sidebar-content">
            <!-- Overview -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="overview">
                    <div class="header-content">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Overview</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('tutor.dashboard') }}" class="sidebar-link" data-tooltip="Dashboard">
                            <i class="fas fa-home"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Schedule -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="schedule">
                    <div class="header-content">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Schedule</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('tutor.schedule') }}" class="sidebar-link" data-tooltip="Calendar">
                            <i class="fas fa-calendar-week"></i>
                            <span>Calendar</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('tutor.schedule') }}?status=upcoming" class="sidebar-link" data-tooltip="Upcoming Appointments">
                            <i class="fas fa-clock"></i>
                            <span>Upcoming</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('tutor.schedule') }}?status=past" class="sidebar-link" data-tooltip="Past Appointments">
                            <i class="fas fa-history"></i>
                            <span>Past</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Clients -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="clients">
                    <div class="header-content">
                        <i class="fas fa-users"></i>
                        <span>Clients</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('tutor.client_list') }}" class="sidebar-link" data-tooltip="My Clients">
                            <i class="fas fa-user-friends"></i>
                            <span>My Clients</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Payments -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="payments">
                    <div class="header-content">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Payments</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('tutor.payments') }}" class="sidebar-link" data-tooltip="My Payments">
                            <i class="fas fa-dollar-sign"></i>
                            <span>My Payments</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Profile -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="profile">
                    <div class="header-content">
                        <i class="fas fa-user-circle"></i>
                        <span>Profile</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('tutor.profile') }}" class="sidebar-link" data-tooltip="Personal Info">
                            <i class="fas fa-id-card"></i>
                            <span>Personal Info</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('tutor.availability') }}" class="sidebar-link" data-tooltip="Availability">
                            <i class="fas fa-clock"></i>
                            <span>Availability</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('tutor.time_off_requests') }}" class="sidebar-link" data-tooltip="Time-Off Requests">
                            <i class="fas fa-calendar-minus"></i>
                            <span>Time-Off Requests</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>
    {% endif %}

    <!-- Left Sidebar (Client only) -->
    {% if current_user.is_authenticated and current_user.role in ['client', 'parent'] %}
    <div class="sidebar" id="sidebar">
        <div class="sidebar-content">
            <!-- Overview -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="overview">
                    <div class="header-content">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>{{ t('navigation.overview') }}</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('client.dashboard') }}" class="sidebar-link" data-tooltip="{{ t('navigation.dashboard') }}">
                            <i class="fas fa-home"></i>
                            <span>{{ t('navigation.dashboard') }}</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Appointments -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="appointments">
                    <div class="header-content">
                        <i class="fas fa-calendar-alt"></i>
                        <span>{{ t('navigation.appointments') }}</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('client.appointments') }}" class="sidebar-link" data-tooltip="{{ t('appointments.all') }}">
                            <i class="fas fa-list"></i>
                            <span>{{ t('appointments.all') }}</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('client.appointments') }}?status=upcoming" class="sidebar-link" data-tooltip="{{ t('appointments.upcoming') }}">
                            <i class="fas fa-clock"></i>
                            <span>{{ t('appointments.upcoming') }}</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('client.appointments') }}?status=past" class="sidebar-link" data-tooltip="{{ t('appointments.past') }}">
                            <i class="fas fa-history"></i>
                            <span>{{ t('appointments.past') }}</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Related Clients -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="clients">
                    <div class="header-content">
                        <i class="fas fa-users"></i>
                        <span>{{ t('navigation.related_clients') }}</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('client.dashboard') }}#related-clients" class="sidebar-link" data-tooltip="{{ t('navigation.related_clients') }}">
                            <i class="fas fa-user-friends"></i>
                            <span>{{ t('navigation.related_clients') }}</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Finances -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="finances">
                    <div class="header-content">
                        <i class="fas fa-dollar-sign"></i>
                        <span>{{ t('navigation.finances') }}</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('client.invoices') }}" class="sidebar-link" data-tooltip="{{ t('invoices.all') }}">
                            <i class="fas fa-file-invoice-dollar"></i>
                            <span>{{ t('invoices.all') }}</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('client.invoices') }}?status=unpaid" class="sidebar-link" data-tooltip="{{ t('invoices.unpaid') }}">
                            <i class="fas fa-exclamation-circle"></i>
                            <span>{{ t('invoices.unpaid') }}</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('client.subscriptions') }}" class="sidebar-link" data-tooltip="{{ t('subscriptions.my_subscriptions') }}">
                            <i class="fas fa-sync"></i>
                            <span>{{ t('subscriptions.my_subscriptions') }}</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Profile -->
            <div class="sidebar-section">
                <div class="sidebar-header" data-section="profile">
                    <div class="header-content">
                        <i class="fas fa-user-circle"></i>
                        <span>{{ t('navigation.profile') }}</span>
                    </div>
                    <i class="fas fa-chevron-down collapse-icon"></i>
                </div>
                <ul class="sidebar-menu">
                    <li>
                        <a href="{{ url_for('client.profile') }}" class="sidebar-link" data-tooltip="{{ t('navigation.profile') }}">
                            <i class="fas fa-id-card"></i>
                            <span>{{ t('navigation.profile') }}</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>
    {% endif %}

    <!-- Legacy Navigation for Non-Sidebar Roles (if any) -->
    {% if current_user.is_authenticated and current_user.role not in ['manager', 'tutor', 'client', 'parent'] %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav me-auto mb-0">
                    {% if current_user.role == 'tutor' %}
                        <!-- Dashboard -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('tutor.dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>

                        <!-- Schedule & Appointments -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="tutorScheduleDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-calendar-alt"></i> Schedule
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('tutor.schedule') }}">
                                    <i class="fas fa-calendar-week"></i> Calendar View
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header">Appointments</h6></li>
                                <li><a class="dropdown-item" href="{{ url_for('tutor.schedule') }}?status=upcoming">
                                    <i class="fas fa-clock"></i> Upcoming Appointments
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('tutor.schedule') }}?status=past">
                                    <i class="fas fa-history"></i> Past Appointments
                                </a></li>
                            </ul>
                        </li>

                        <!-- Clients -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('tutor.client_list') }}">
                                <i class="fas fa-users"></i> Clients
                            </a>
                        </li>

                        <!-- Payments -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('tutor.payments') }}">
                                <i class="fas fa-money-bill-wave"></i> Payments
                            </a>
                        </li>

                        <!-- My Profile -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="profileDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle"></i> My Profile
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('tutor.profile') }}">
                                    <i class="fas fa-id-card"></i> Personal Info
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('tutor.availability') }}">
                                    <i class="fas fa-clock"></i> Availability
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('tutor.time_off_requests') }}">
                                    <i class="fas fa-calendar-minus"></i> Time-Off Requests
                                </a></li>
                            </ul>
                        </li>
                    {% elif current_user.role in ['parent', 'client'] %}
                        <!-- Dashboard -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('client.dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i> {{ t('navigation.dashboard') }}
                            </a>
                        </li>

                        <!-- Appointments -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="parentAppointmentsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-calendar-alt"></i> {{ t('navigation.appointments') }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('client.appointments') }}?status=upcoming">
                                    <i class="fas fa-clock"></i> {{ t('appointments.upcoming') }}
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('client.appointments') }}?status=past">
                                    <i class="fas fa-history"></i> {{ t('appointments.past') }}
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('client.appointments') }}?status=all">
                                    <i class="fas fa-list"></i> {{ t('appointments.all') }}
                                </a></li>
                            </ul>
                        </li>

                        <!-- Related Clients -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('client.dashboard') }}#related-clients">
                                <i class="fas fa-users"></i> {{ t('navigation.related_clients') }}
                            </a>
                        </li>

                        <!-- Finances -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="parentFinancesDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-dollar-sign"></i> {{ t('navigation.finances') }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><h6 class="dropdown-header">{{ t('navigation.invoices') }}</h6></li>
                                <li><a class="dropdown-item" href="{{ url_for('client.invoices') }}">
                                    <i class="fas fa-file-invoice-dollar"></i> {{ t('invoices.all') }}
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('client.invoices') }}?status=unpaid">
                                    <i class="fas fa-exclamation-circle"></i> {{ t('invoices.unpaid') }}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header">{{ t('navigation.subscriptions') }}</h6></li>
                                <li><a class="dropdown-item" href="{{ url_for('client.subscriptions') }}">
                                    <i class="fas fa-sync"></i> {{ t('subscriptions.my_subscriptions') }}
                                </a></li>
                            </ul>
                        </li>

                        <!-- Profile -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('client.profile') }}">
                                <i class="fas fa-user-circle"></i> {{ t('navigation.profile') }}
                            </a>
                        </li>
                    {% endif %}
                </ul>

                <ul class="navbar-nav ms-2">
                    {% if current_user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="legacyUserDropdown" role="button" data-bs-toggle="dropdown">
                                {{ current_user.email }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                {% if current_user.role == 'tutor' %}
                                    <li><a class="dropdown-item" href="{{ url_for('tutor.profile') }}">{{ t('navigation.profile') }}</a></li>
                                {% elif current_user.role in ['parent', 'client'] %}
                                    <li><a class="dropdown-item" href="{{ url_for('client.profile') }}">{{ t('navigation.profile') }}</a></li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li class="dropdown-item">
                                    <div class="btn-group btn-group-sm w-100" role="group" aria-label="{{ t('language.select') }}">
                                        <button type="button" class="btn btn-outline-primary language-btn {% if session.get('language', 'en') == 'en' %}active{% endif %}" data-lang="en">{{ t('language.english') }}</button>
                                        <button type="button" class="btn btn-outline-primary language-btn {% if session.get('language', 'en') == 'fr' %}active{% endif %}" data-lang="fr">{{ t('language.french') }}</button>
                                    </div>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">{{ t('navigation.logout') }}</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.login') }}">{{ t('navigation.login') }}</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Main Content -->
    <div class="main-content {% if current_user.is_authenticated and current_user.role in ['manager', 'tutor', 'client', 'parent'] %}with-sidebar{% endif %}" id="mainContent">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category if category != 'message' else 'info' }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Page Content -->
        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">&copy; {{ now.year }} TutorAide Inc. All rights reserved.</span>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery (for Ajax) -->
    <script src="https://code.jquery.com/jquery-3.6.3.min.js"></script>

    <!-- FullCalendar (for scheduling) -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.0/main.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- Role-specific Navigation JS -->
    {% if current_user.is_authenticated %}
        {% if current_user.role == 'manager' %}
            <script src="{{ url_for('static', filename='js/manager_navigation.js') }}"></script>
            <script src="{{ url_for('static', filename='js/sidebar.js') }}"></script>
        {% elif current_user.role == 'tutor' %}
            <script src="{{ url_for('static', filename='js/tutor_navigation.js') }}"></script>
            <script src="{{ url_for('static', filename='js/sidebar.js') }}"></script>
        {% elif current_user.role in ['parent', 'client'] %}
            <script src="{{ url_for('static', filename='js/client_navigation.js') }}"></script>
            <script src="{{ url_for('static', filename='js/sidebar.js') }}"></script>
        {% endif %}
    {% endif %}

    <!-- CSRF Protection -->
    <script src="{{ url_for('static', filename='js/csrf.js') }}"></script>

    <!-- Translations and Language Switching -->
    <script>
        // Load translations from JSON
        const translations = {
            en: {{ load_translations('en')|tojson }},
            fr: {{ load_translations('fr')|tojson }}
        };

        // Current locale
        let currentLocale = '{{ session.get("language", "en") }}';

        // Translation function
        function t(key) {
            const parts = key.split('.');
            let value = translations[currentLocale];

            for (const part of parts) {
                if (value && typeof value === 'object' && part in value) {
                    value = value[part];
                } else {
                    // Fallback to English if key not found
                    let fallback = translations['en'];
                    for (const p of parts) {
                        if (fallback && typeof fallback === 'object' && p in fallback) {
                            fallback = fallback[p];
                        } else {
                            return key;
                        }
                    }
                    return fallback || key;
                }
            }

            return value || key;
        }

        // Make t available globally
        window.t = t;

        document.addEventListener('DOMContentLoaded', function() {
            // Get all language buttons
            const languageButtons = document.querySelectorAll('.language-btn');

            // Add click event to each button
            languageButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const lang = this.getAttribute('data-lang');

                    // Get CSRF token
                    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                    // Send request to set language
                    fetch('/api/set-language', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': csrfToken
                        },
                        body: JSON.stringify({ language: lang }),
                        credentials: 'same-origin'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Reload the page to apply the language change
                            window.location.reload();
                        }
                    })
                    .catch(error => {
                        console.error('Error setting language:', error);
                    });
                });
            });
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>