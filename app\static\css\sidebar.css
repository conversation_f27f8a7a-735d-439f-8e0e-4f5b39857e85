/* Sidebar Navigation Styles - Apple-inspired UX Design */

/* Variables for consistent theming */
:root {
    --sidebar-width: 280px;
    --sidebar-width-collapsed: 60px;
    --sidebar-bg: #f8f9fa;
    --sidebar-border: #e9ecef;
    --sidebar-text: #495057;
    --sidebar-text-hover: #212529;
    --sidebar-active-bg: #007bff;
    --sidebar-active-text: #ffffff;
    --sidebar-header-bg: #e9ecef;
    --sidebar-header-text: #6c757d;
    --topbar-height: 56px;
    --transition-speed: 0.3s;
    --section-collapsed-bg: #ffffff;
}

/* Earth Tone Theme Variables for Client Interface */
.client-interface {
    --sidebar-bg: linear-gradient(180deg, #fefefe 0%, #faf8f5 100%);
    --sidebar-border: #e8e2d5;
    --sidebar-text: #3c3c3c;
    --sidebar-text-hover: #212529;
    --sidebar-active-bg: linear-gradient(135deg, #e8e2d5 0%, #9aaa97 100%);
    --sidebar-active-text: #3c3c3c;
    --sidebar-header-bg: linear-gradient(135deg, #f7f5f1 0%, #f2f4f2 100%);
    --sidebar-header-text: #3c3c3c;
    --section-collapsed-bg: linear-gradient(135deg, #fefefe 0%, #faf8f5 100%);
}

/* Body adjustments for fixed navbar */
body {
    padding-top: var(--topbar-height);
}

/* Top Navigation Bar */
.navbar.fixed-top {
    z-index: 1030;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Quick Actions in Top Bar */
.quick-actions-top .btn {
    border-radius: 6px;
    transition: all var(--transition-speed) ease;
}

.quick-actions-top .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: var(--topbar-height);
    left: 0;
    width: var(--sidebar-width);
    height: calc(100vh - var(--topbar-height));
    background: var(--sidebar-bg);
    border-right: 1px solid var(--sidebar-border);
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 1020;
    transition: width var(--transition-speed) ease, transform var(--transition-speed) ease;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

/* Earth tone sidebar enhancements for client interface */
.client-interface .sidebar {
    box-shadow: 4px 0 20px rgba(139, 115, 85, 0.12);
    border-right: 1px solid var(--sidebar-border);
}

/* Collapsed sidebar */
.sidebar.collapsed {
    width: var(--sidebar-width-collapsed);
}

/* COMPLETELY HIDE text and arrows in collapsed state */
.sidebar.collapsed .sidebar-header .header-content span {
    display: none !important;
}

.sidebar.collapsed .sidebar-header .collapse-icon {
    display: none !important;
}

/* ADDITIONAL RULES TO HIDE ALL CHEVRON ARROWS */
.sidebar.collapsed .collapse-icon,
.sidebar.collapsed .sidebar-header .collapse-icon,
.sidebar.collapsed .sidebar-section .sidebar-header .collapse-icon,
.sidebar.collapsed i.fa-chevron-down,
.sidebar.collapsed i.fa-chevron-up,
.sidebar.collapsed i.fa-chevron-left,
.sidebar.collapsed i.fa-chevron-right {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

.sidebar.collapsed .sidebar-link span {
    display: none !important;
}

.sidebar.collapsed .badge {
    display: none !important;
}

.sidebar.collapsed .sidebar-menu {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-speed) ease;
}

.sidebar.collapsed .sidebar-section.expanded .sidebar-menu {
    max-height: none;
    overflow: visible;
}

/* Collapsed sidebar - show only icons */
.sidebar.collapsed .sidebar-link {
    justify-content: center;
    padding: 0.75rem;
}

.sidebar.collapsed .sidebar-link i {
    margin-right: 0;
    font-size: 1.1rem;
}

.sidebar-content {
    padding: 1rem 0;
}

/* Sidebar Sections */
.sidebar-section {
    margin-bottom: 0.5rem;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1.5rem;
    background: var(--sidebar-header-bg);
    color: var(--sidebar-header-text);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid var(--sidebar-border);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    user-select: none;
    border-radius: 0 12px 12px 0;
    margin: 2px 8px 2px 0;
}

.sidebar-header:hover {
    background-color: #dee2e6;
}

/* Earth tone sidebar header styling for client interface */
.client-interface .sidebar-header {
    background: var(--sidebar-header-bg);
    border-radius: 0 12px 12px 0;
    margin: 4px 12px 4px 0;
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    letter-spacing: 1px;
    border-bottom: 1px solid var(--sidebar-border);
}

.client-interface .sidebar-header:hover {
    background: linear-gradient(135deg, #e8e2d5 0%, #9aaa97 100%);
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(139, 115, 85, 0.15);
}

.sidebar-header i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
    font-size: 1rem;
    transition: all var(--transition-speed) ease;
}

.sidebar-header .header-content {
    display: flex;
    align-items: center;
    flex: 1;
    transition: all var(--transition-speed) ease;
}

.sidebar-header .header-content i {
    transition: all var(--transition-speed) ease;
}

.sidebar-header .header-content span {
    transition: opacity var(--transition-speed) ease, visibility var(--transition-speed) ease;
}

.sidebar-header .collapse-icon {
    margin-left: auto;
    margin-right: 0;
    transition: transform var(--transition-speed) ease, opacity var(--transition-speed) ease, visibility var(--transition-speed) ease;
    font-size: 0.75rem;
}

.sidebar-section.collapsed .collapse-icon {
    transform: rotate(-90deg);
}

/* Sidebar Menu */
.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 1000px;
    overflow: hidden;
    transition: max-height var(--transition-speed) ease;
}

.sidebar-section.collapsed .sidebar-menu {
    max-height: 0;
}

.sidebar-menu li {
    margin: 0;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: all var(--transition-speed) ease;
    border-left: 3px solid transparent;
    position: relative;
    border-radius: 0 12px 12px 0;
    margin: 2px 0;
}

.sidebar-link:hover {
    color: var(--sidebar-text-hover);
    background-color: rgba(0, 123, 255, 0.1);
    text-decoration: none;
    border-left-color: var(--sidebar-active-bg);
}

.sidebar-link.active,
.sidebar-link[aria-current="page"] {
    color: var(--sidebar-active-text);
    background: var(--sidebar-active-bg);
    border-left-color: var(--sidebar-active-bg);
    font-weight: 500;
}

/* Earth tone sidebar link styling for client interface */
.client-interface .sidebar-link {
    border-radius: 0 12px 12px 0;
    margin: 4px 8px 4px 0;
    font-weight: 500;
}

.client-interface .sidebar-link:hover {
    background: linear-gradient(135deg, #f7f5f1 0%, #f2f4f2 100%);
    color: var(--sidebar-text-hover);
    border-left-color: #8b7355;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(139, 115, 85, 0.15);
}

.client-interface .sidebar-link.active,
.client-interface .sidebar-link[aria-current="page"] {
    background: var(--sidebar-active-bg);
    color: var(--sidebar-active-text);
    border-left-color: #8b7355;
    box-shadow: 0 4px 16px rgba(139, 115, 85, 0.2);
    font-weight: 600;
}

.sidebar-link i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
    font-size: 0.875rem;
}

.sidebar-link span {
    flex: 1;
    font-size: 0.9rem;
}

/* Badge styles for notifications */
.sidebar-link .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
}

/* Main Content Adjustments */
.main-content {
    padding: 2rem;
    transition: margin-left var(--transition-speed) ease;
}

.main-content.with-sidebar {
    margin-left: var(--sidebar-width);
}

.main-content.with-sidebar.sidebar-collapsed {
    margin-left: var(--sidebar-width-collapsed);
}

/* Sidebar Toggle Button */
.sidebar-toggle {
    border: none !important;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    transition: all var(--transition-speed) ease;
    background-color: transparent;
    color: white;
    font-size: 1.1rem;
}

.sidebar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: scale(1.05);
}

.sidebar-toggle:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
    color: white;
}

.sidebar-toggle i {
    font-size: 1.1rem;
    display: inline-block;
    line-height: 1;
}

/* Ensure hamburger icon is always visible */
.sidebar-toggle i.fa-bars {
    opacity: 1;
    visibility: visible;
    color: white;
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1010;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-speed) ease;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Mobile Responsive */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content.with-sidebar {
        margin-left: 0;
    }

    .quick-actions-top {
        display: none !important;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
    }

    .sidebar-link {
        padding: 1rem 1.5rem;
    }

    .sidebar-link span {
        font-size: 1rem;
    }
}

/* Search Bar Enhancements */
.navbar-search-form .form-control {
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transition: all var(--transition-speed) ease;
}

.navbar-search-form .form-control:focus {
    background-color: white;
    color: #495057;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.navbar-search-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.navbar-search-form .form-control:focus::placeholder {
    color: #6c757d;
}

.navbar-search-form .btn {
    border-radius: 0 20px 20px 0;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-left: none;
}

/* Smooth scrolling for sidebar */
.sidebar {
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 transparent;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: #dee2e6;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background-color: #adb5bd;
}

/* Earth tone scrollbar styling for client interface */
.client-interface .sidebar {
    scrollbar-color: #e8e2d5 transparent;
}

.client-interface .sidebar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #e8e2d5 0%, #9aaa97 100%);
    border-radius: 4px;
}

.client-interface .sidebar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #8b7355 0%, #9aaa97 100%);
}

/* Earth tone icon colors for client interface */
.client-interface .sidebar-link i {
    color: #8b7355;
    transition: color 0.3s ease;
}

.client-interface .sidebar-link:hover i {
    color: #9aaa97;
}

.client-interface .sidebar-link.active i,
.client-interface .sidebar-link[aria-current="page"] i {
    color: #3c3c3c;
}

.client-interface .sidebar-header i {
    color: #8b7355;
}

.client-interface .sidebar-header:hover i {
    color: #9aaa97;
}

/* Animation for sidebar sections */
.sidebar-section {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus states for accessibility */
.sidebar-link:focus {
    outline: 2px solid var(--sidebar-active-bg);
    outline-offset: -2px;
}

.sidebar-toggle:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* Tooltip for collapsed sidebar */
.sidebar.collapsed .sidebar-link {
    position: relative;
}

.sidebar.collapsed .sidebar-link:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background-color: #333;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    font-size: 0.875rem;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 0.5rem;
    opacity: 0;
    animation: fadeInTooltip 0.2s ease-in-out forwards;
}

.sidebar.collapsed .sidebar-link:hover::before {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-right-color: #333;
    z-index: 1000;
    margin-left: -5px;
    opacity: 0;
    animation: fadeInTooltip 0.2s ease-in-out forwards;
}

@keyframes fadeInTooltip {
    to {
        opacity: 1;
    }
}

/* COLLAPSED SIDEBAR HEADER STYLING - COMPLETE REWRITE */
.sidebar.collapsed .sidebar-header {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 0.75rem !important;
    border-bottom: 1px solid var(--sidebar-border);
    position: relative;
}

.sidebar.collapsed .sidebar-header .header-content {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
}

/* FORCE ICONS TO BE VISIBLE IN COLLAPSED STATE - SAME SIZE AS NORMAL */
.sidebar.collapsed .sidebar-header .header-content i {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    margin: 0 !important;
    font-size: 1rem !important; /* Same size as normal state */
    color: var(--sidebar-header-text) !important;
    text-align: center !important;
    width: 20px !important; /* Same width as normal state */
}

/* MAXIMUM SPECIFICITY FOR ICONS - SAME SIZE AS NORMAL */
.sidebar.collapsed .sidebar-section .sidebar-header .header-content i.fas,
.sidebar.collapsed .sidebar-section .sidebar-header .header-content i.fa,
.sidebar.collapsed .sidebar-header i.fas,
.sidebar.collapsed .sidebar-header i.fa,
.sidebar.collapsed .sidebar-header .header-content i {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    color: var(--sidebar-header-text) !important;
    font-size: 1rem !important; /* Same size as normal state */
    margin: 0 !important;
    text-align: center !important;
    width: 20px !important; /* Same width as normal state */
}

/* Better spacing for collapsed state */
.sidebar.collapsed .sidebar-section {
    margin-bottom: 0.25rem;
    position: relative;
}

.sidebar.collapsed .sidebar-content {
    padding: 0.5rem 0;
}

/* Collapsed sidebar - show sections as icon-only buttons */
.sidebar.collapsed .sidebar-header:hover {
    background-color: rgba(0, 123, 255, 0.1);
    cursor: pointer;
}

/* Add visual feedback for collapsed sections */
.sidebar.collapsed .sidebar-section:hover .sidebar-header {
    background-color: rgba(0, 123, 255, 0.15);
    transform: translateX(2px);
}

/* Show menu on hover in collapsed state */
.sidebar.collapsed .sidebar-section:hover .sidebar-menu {
    position: absolute;
    left: 100%;
    top: 0;
    width: 220px;
    max-height: none;
    background-color: var(--sidebar-bg);
    border: 1px solid var(--sidebar-border);
    border-radius: 0 8px 8px 0;
    box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15);
    z-index: 1030;
    padding: 0.5rem 0;
}

.sidebar.collapsed .sidebar-section:hover .sidebar-menu .sidebar-link {
    justify-content: flex-start;
    padding: 0.75rem 1.5rem;
}

.sidebar.collapsed .sidebar-section:hover .sidebar-menu .sidebar-link i {
    margin-right: 0.75rem;
    font-size: 0.875rem;
}

.sidebar.collapsed .sidebar-section:hover .sidebar-menu .sidebar-link span,
.sidebar.collapsed .sidebar-section:hover .sidebar-menu .badge {
    opacity: 1;
    visibility: visible;
}
