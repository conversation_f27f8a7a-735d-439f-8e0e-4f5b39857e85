/* Feminine & Sophisticated Client Interface Theme */
/* Pastel Color Palette with Elegant Design */

/* ===== COLOR VARIABLES ===== */
:root {
    /* Primary Pastel Colors */
    --primary-blush: #f8d7da;
    --primary-blush-dark: #e8a2a8;
    --primary-blush-light: #fdf2f3;
    
    /* Secondary Pastels */
    --lavender: #e6e6fa;
    --lavender-dark: #d1c4e9;
    --lavender-light: #f3f0ff;
    
    --mint: #d4f1d4;
    --mint-dark: #a8d8a8;
    --mint-light: #f0fdf0;
    
    --peach: #ffeaa7;
    --peach-dark: #fdcb6e;
    --peach-light: #fffbf0;
    
    --cream: #fef7f0;
    --cream-dark: #f4e4d6;
    
    /* Neutral Sophistication */
    --soft-gray: #f8f9fa;
    --warm-gray: #6c757d;
    --charcoal: #495057;
    --white-pearl: #ffffff;
    
    /* Accent Colors */
    --rose-gold: #e8b4b8;
    --dusty-rose: #d4a5a5;
    --sage-green: #9caf88;
    --soft-purple: #b19cd9;
    
    /* Shadows & Effects */
    --soft-shadow: 0 4px 20px rgba(248, 215, 218, 0.15);
    --elegant-shadow: 0 8px 32px rgba(248, 215, 218, 0.2);
    --inner-glow: inset 0 1px 3px rgba(255, 255, 255, 0.8);
    
    /* Typography */
    --font-elegant: 'Playfair Display', serif;
    --font-modern: 'Inter', sans-serif;
    --font-accent: 'Dancing Script', cursive;
}

/* ===== GOOGLE FONTS IMPORT ===== */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&family=Dancing+Script:wght@400;500;600&display=swap');

/* ===== GLOBAL CLIENT INTERFACE STYLING ===== */
.client-interface {
    font-family: var(--font-modern);
    background: linear-gradient(135deg, var(--cream) 0%, var(--primary-blush-light) 100%);
    min-height: 100vh;
}

/* ===== NAVIGATION ENHANCEMENTS ===== */
.navbar-dark.bg-primary {
    background: linear-gradient(135deg, var(--primary-blush-dark) 0%, var(--lavender-dark) 100%) !important;
    box-shadow: var(--soft-shadow);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
    font-family: var(--font-elegant);
    font-weight: 600;
    color: var(--white-pearl) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== SIDEBAR STYLING ===== */
.sidebar {
    background: linear-gradient(180deg, var(--white-pearl) 0%, var(--cream) 100%);
    border-right: 1px solid var(--primary-blush);
    box-shadow: var(--soft-shadow);
}

.sidebar .nav-link {
    color: var(--charcoal);
    border-radius: 12px;
    margin: 4px 8px;
    padding: 12px 16px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sidebar .nav-link:hover {
    background: linear-gradient(135deg, var(--primary-blush-light) 0%, var(--lavender-light) 100%);
    color: var(--charcoal);
    transform: translateX(4px);
    box-shadow: var(--soft-shadow);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, var(--primary-blush) 0%, var(--lavender) 100%);
    color: var(--charcoal);
    box-shadow: var(--elegant-shadow);
}

/* ===== CARD COMPONENTS ===== */
.card {
    border: none;
    border-radius: 20px;
    box-shadow: var(--soft-shadow);
    background: var(--white-pearl);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--elegant-shadow);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-blush) 0%, var(--lavender) 100%) !important;
    border-bottom: none;
    padding: 20px 24px;
    color: var(--charcoal) !important;
    font-family: var(--font-elegant);
    font-weight: 600;
    font-size: 1.1rem;
}

.card-body {
    padding: 24px;
    background: var(--white-pearl);
}

/* ===== BUTTONS ===== */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-blush-dark) 0%, var(--lavender-dark) 100%);
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-weight: 500;
    color: var(--white-pearl);
    box-shadow: var(--soft-shadow);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--dusty-rose) 0%, var(--soft-purple) 100%);
    transform: translateY(-2px);
    box-shadow: var(--elegant-shadow);
}

.btn-success {
    background: linear-gradient(135deg, var(--mint-dark) 0%, var(--sage-green) 100%);
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-weight: 500;
    color: var(--white-pearl);
    box-shadow: var(--soft-shadow);
}

.btn-outline-primary {
    border: 2px solid var(--primary-blush-dark);
    color: var(--primary-blush-dark);
    border-radius: 25px;
    padding: 10px 22px;
    font-weight: 500;
    background: transparent;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, var(--primary-blush) 0%, var(--lavender) 100%);
    border-color: var(--primary-blush-dark);
    color: var(--charcoal);
    transform: translateY(-2px);
}

/* ===== FORM ELEMENTS ===== */
.form-control {
    border: 2px solid var(--primary-blush);
    border-radius: 15px;
    padding: 12px 16px;
    background: var(--white-pearl);
    transition: all 0.3s ease;
    font-family: var(--font-modern);
}

.form-control:focus {
    border-color: var(--lavender-dark);
    box-shadow: 0 0 0 0.2rem rgba(209, 196, 233, 0.25);
    background: var(--lavender-light);
}

.form-label {
    font-weight: 500;
    color: var(--charcoal);
    margin-bottom: 8px;
    font-family: var(--font-modern);
}

/* ===== ALERTS ===== */
.alert {
    border: none;
    border-radius: 15px;
    padding: 16px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(135deg, var(--mint-light) 0%, var(--mint) 100%);
    color: var(--charcoal);
    border-left: 4px solid var(--sage-green);
}

.alert-info {
    background: linear-gradient(135deg, var(--lavender-light) 0%, var(--lavender) 100%);
    color: var(--charcoal);
    border-left: 4px solid var(--soft-purple);
}

.alert-warning {
    background: linear-gradient(135deg, var(--peach-light) 0%, var(--peach) 100%);
    color: var(--charcoal);
    border-left: 4px solid var(--peach-dark);
}

.alert-danger {
    background: linear-gradient(135deg, var(--primary-blush-light) 0%, var(--primary-blush) 100%);
    color: var(--charcoal);
    border-left: 4px solid var(--dusty-rose);
}

/* ===== TABLES ===== */
.table {
    background: var(--white-pearl);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--soft-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-blush) 0%, var(--lavender) 100%);
    color: var(--charcoal);
    border: none;
    padding: 16px;
    font-weight: 600;
    font-family: var(--font-elegant);
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: var(--cream);
    transform: scale(1.01);
}

.table tbody td {
    padding: 16px;
    border-color: var(--primary-blush);
    vertical-align: middle;
}

/* ===== BADGES ===== */
.badge {
    border-radius: 20px;
    padding: 6px 12px;
    font-weight: 500;
    font-size: 0.75rem;
}

.badge.bg-success {
    background: linear-gradient(135deg, var(--mint-dark) 0%, var(--sage-green) 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, var(--peach) 0%, var(--peach-dark) 100%) !important;
    color: var(--charcoal) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, var(--primary-blush-dark) 0%, var(--dusty-rose) 100%) !important;
}

/* ===== DASHBOARD SPECIFIC ===== */
.dashboard-welcome {
    background: linear-gradient(135deg, var(--cream) 0%, var(--primary-blush-light) 100%);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: var(--soft-shadow);
}

.dashboard-welcome h2 {
    font-family: var(--font-elegant);
    color: var(--charcoal);
    margin-bottom: 8px;
}

.dashboard-welcome p {
    color: var(--warm-gray);
    font-size: 1.1rem;
}

/* ===== STATISTICS CARDS ===== */
.stat-card {
    background: linear-gradient(135deg, var(--white-pearl) 0%, var(--cream) 100%);
    border-radius: 20px;
    padding: 24px;
    text-align: center;
    box-shadow: var(--soft-shadow);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--elegant-shadow);
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    font-family: var(--font-elegant);
    background: linear-gradient(135deg, var(--primary-blush-dark) 0%, var(--lavender-dark) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-card .stat-label {
    color: var(--warm-gray);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .card {
        border-radius: 15px;
        margin-bottom: 16px;
    }
    
    .card-header {
        padding: 16px 20px;
        font-size: 1rem;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .btn {
        border-radius: 20px;
        padding: 10px 20px;
    }
    
    .dashboard-welcome {
        padding: 24px;
        margin-bottom: 24px;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--cream);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-blush) 0%, var(--lavender) 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-blush-dark) 0%, var(--lavender-dark) 100%);
}
